#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新模块GUI界面
提供配置组的树形管理、批量更新、缓存查看等功能
"""

import asyncio
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QPushButton, QTextEdit, QProgressBar, QLabel, QGroupBox, QSplitter,
    QCheckBox, QComboBox, QSpinBox, QMessageBox, QMenu, QAction,
    QHeaderView, QAbstractItemView
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont
from core.update_manager import update_manager
from gui.utils import show_info_message, show_error_message, show_warning_message


class UpdateThread(QThread):
    """更新线程"""
    progress_updated = pyqtSignal(str, int, int, str)  # config_name, current, total, message
    log_updated = pyqtSignal(str)  # log message
    finished_signal = pyqtSignal(dict)  # result
    
    def __init__(self, config_names, update_type="single"):
        super().__init__()
        self.config_names = config_names
        self.update_type = update_type
        self.is_cancelled = False
    
    def run(self):
        """执行更新任务"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.update_type == "single" and len(self.config_names) == 1:
                result = loop.run_until_complete(
                    update_manager.update_single_config(
                        self.config_names[0],
                        self.progress_callback,
                        self.log_callback
                    )
                )
            elif self.update_type == "multiple":
                result = loop.run_until_complete(
                    update_manager.update_multiple_configs(
                        self.config_names,
                        self.progress_callback,
                        self.log_callback
                    )
                )
            elif self.update_type == "all":
                result = loop.run_until_complete(
                    update_manager.update_all_configs(
                        self.progress_callback,
                        self.log_callback
                    )
                )
            else:
                result = {"success": False, "error": "未知的更新类型"}
            
            self.finished_signal.emit(result)
            
        except Exception as e:
            self.finished_signal.emit({"success": False, "error": str(e)})
        finally:
            loop.close()
    
    def progress_callback(self, config_name, current, total, message=""):
        """进度回调"""
        if not self.is_cancelled:
            self.progress_updated.emit(config_name, current, total, message)
    
    def log_callback(self, message):
        """日志回调"""
        if not self.is_cancelled:
            self.log_updated.emit(message)
    
    def cancel(self):
        """取消更新"""
        self.is_cancelled = True
        update_manager.stop_update()


class UpdateTab(QWidget):
    """更新模块标签页"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.update_thread = None
        self.init_ui()
        self.load_config_tree()
        
        # 定时刷新状态
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(5000)  # 每5秒刷新一次
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：配置组树形结构
        left_widget = self.create_config_tree_widget()
        splitter.addWidget(left_widget)
        
        # 右侧：更新控制和日志
        right_widget = self.create_update_control_widget()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        layout.addWidget(splitter)
        self.setLayout(layout)
    
    def create_config_tree_widget(self):
        """创建配置组树形控件"""
        widget = QGroupBox("配置组管理")
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_config_tree)
        toolbar_layout.addWidget(self.refresh_btn)
        
        self.expand_all_btn = QPushButton("展开全部")
        self.expand_all_btn.clicked.connect(self.expand_all)
        toolbar_layout.addWidget(self.expand_all_btn)
        
        self.collapse_all_btn = QPushButton("收起全部")
        self.collapse_all_btn.clicked.connect(self.collapse_all)
        toolbar_layout.addWidget(self.collapse_all_btn)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # 配置组树
        self.config_tree = QTreeWidget()
        self.config_tree.setHeaderLabels([
            "配置组", "最后更新", "文章数", "成功率", "状态"
        ])
        self.config_tree.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.config_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.config_tree.customContextMenuRequested.connect(self.show_context_menu)
        
        # 设置列宽
        header = self.config_tree.header()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.config_tree)
        
        # 选择统计
        self.selection_label = QLabel("未选择任何配置组")
        layout.addWidget(self.selection_label)
        
        widget.setLayout(layout)
        return widget
    
    def create_update_control_widget(self):
        """创建更新控制控件"""
        widget = QGroupBox("更新控制")
        layout = QVBoxLayout()
        
        # 更新选项
        options_layout = QHBoxLayout()
        
        options_layout.addWidget(QLabel("并发数:"))
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 10)
        self.concurrent_spin.setValue(3)
        options_layout.addWidget(self.concurrent_spin)
        
        options_layout.addWidget(QLabel("更新模式:"))
        self.update_mode_combo = QComboBox()
        self.update_mode_combo.addItems(["增量更新", "完整更新"])
        options_layout.addWidget(self.update_mode_combo)
        
        self.auto_refresh_check = QCheckBox("自动刷新状态")
        self.auto_refresh_check.setChecked(True)
        options_layout.addWidget(self.auto_refresh_check)
        
        options_layout.addStretch()
        layout.addLayout(options_layout)
        
        # 更新按钮
        button_layout = QHBoxLayout()
        
        self.update_selected_btn = QPushButton("更新选中")
        self.update_selected_btn.clicked.connect(self.update_selected)
        button_layout.addWidget(self.update_selected_btn)
        
        self.update_all_btn = QPushButton("更新全部")
        self.update_all_btn.clicked.connect(self.update_all)
        button_layout.addWidget(self.update_all_btn)
        
        self.stop_btn = QPushButton("停止更新")
        self.stop_btn.clicked.connect(self.stop_update)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("")
        layout.addWidget(self.progress_label)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlaceholderText("更新日志将在这里显示...")
        layout.addWidget(self.log_text)
        
        # 清空日志按钮
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        layout.addWidget(clear_log_btn)
        
        widget.setLayout(layout)
        return widget
    
    def load_config_tree(self):
        """加载配置组树"""
        self.config_tree.clear()
        
        try:
            categories = update_manager.get_all_categories()
            
            for category_name, category_data in categories.items():
                # 创建分类节点
                category_item = QTreeWidgetItem(self.config_tree)
                category_item.setText(0, f"📁 {category_name}")
                category_item.setText(4, f"{len(category_data['configs'])} 个配置")
                category_item.setData(0, Qt.UserRole, {"type": "category", "name": category_name})
                
                # 设置分类节点样式
                font = QFont()
                font.setBold(True)
                category_item.setFont(0, font)
                
                # 添加配置组节点
                for config_info in category_data["configs"]:
                    config_item = QTreeWidgetItem(category_item)
                    config_item.setText(0, config_info["name"])
                    
                    # 设置更新信息
                    cache = config_info["cache"]
                    last_update = cache.get("last_update")
                    if last_update:
                        try:
                            dt = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                            config_item.setText(1, dt.strftime("%m-%d %H:%M"))
                        except:
                            config_item.setText(1, "解析失败")
                    else:
                        config_item.setText(1, "从未更新")
                    
                    config_item.setText(2, str(cache.get("total_articles", 0)))
                    
                    success_rate = cache.get("success_rate", 0.0)
                    config_item.setText(3, f"{success_rate:.1%}")
                    
                    # 获取更新状态
                    update_info = update_manager.get_config_update_info(config_info["name"])
                    if update_info["can_update"]:
                        config_item.setText(4, "✅ 可更新")
                    else:
                        config_item.setText(4, "⏳ 等待中")
                    
                    config_item.setData(0, Qt.UserRole, {
                        "type": "config", 
                        "name": config_info["name"],
                        "update_info": update_info
                    })
                
                # 展开分类
                category_item.setExpanded(True)
            
            self.update_selection_label()
            
        except Exception as e:
            self.log_message(f"❌ 加载配置组树失败: {e}")
    
    def update_selection_label(self):
        """更新选择标签"""
        selected_configs = self.get_selected_configs()
        if selected_configs:
            self.selection_label.setText(f"已选择 {len(selected_configs)} 个配置组")
        else:
            self.selection_label.setText("未选择任何配置组")
    
    def get_selected_configs(self):
        """获取选中的配置组"""
        selected_configs = []
        for item in self.config_tree.selectedItems():
            data = item.data(0, Qt.UserRole)
            if data and data.get("type") == "config":
                selected_configs.append(data["name"])
        return selected_configs

    def expand_all(self):
        """展开所有节点"""
        self.config_tree.expandAll()

    def collapse_all(self):
        """收起所有节点"""
        self.config_tree.collapseAll()

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.config_tree.itemAt(position)
        if not item:
            return

        data = item.data(0, Qt.UserRole)
        if not data:
            return

        menu = QMenu(self)

        if data.get("type") == "config":
            # 配置组右键菜单
            update_action = QAction("🔄 更新此配置", self)
            update_action.triggered.connect(lambda: self.update_single_config(data["name"]))
            menu.addAction(update_action)

            view_cache_action = QAction("📊 查看缓存", self)
            view_cache_action.triggered.connect(lambda: self.view_config_cache(data["name"]))
            menu.addAction(view_cache_action)

            clear_cache_action = QAction("🗑️ 清空缓存", self)
            clear_cache_action.triggered.connect(lambda: self.clear_config_cache(data["name"]))
            menu.addAction(clear_cache_action)

        elif data.get("type") == "category":
            # 分类右键菜单
            update_category_action = QAction("🔄 更新此分类", self)
            update_category_action.triggered.connect(lambda: self.update_category(data["name"]))
            menu.addAction(update_category_action)

        menu.exec_(self.config_tree.mapToGlobal(position))

    def update_selected(self):
        """更新选中的配置组"""
        selected_configs = self.get_selected_configs()
        if not selected_configs:
            show_warning_message(self, "警告", "请先选择要更新的配置组")
            return

        self.start_update(selected_configs, "multiple")

    def update_all(self):
        """更新所有配置组"""
        reply = QMessageBox.question(
            self, "确认", "确定要更新所有配置组吗？这可能需要较长时间。",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.start_update([], "all")

    def update_single_config(self, config_name):
        """更新单个配置组"""
        self.start_update([config_name], "single")

    def update_category(self, category_name):
        """更新整个分类的配置组"""
        try:
            categories = update_manager.get_all_categories()
            category_data = categories.get(category_name, {})
            config_names = [config["name"] for config in category_data.get("configs", [])]

            if config_names:
                self.start_update(config_names, "multiple")
            else:
                show_info_message(self, "提示", f"分类 '{category_name}' 下没有配置组")
        except Exception as e:
            show_error_message(self, "错误", f"获取分类配置失败: {e}")

    def start_update(self, config_names, update_type):
        """开始更新任务"""
        if self.update_thread and self.update_thread.isRunning():
            show_warning_message(self, "警告", "已有更新任务在运行中")
            return

        # 创建更新线程
        self.update_thread = UpdateThread(config_names, update_type)
        self.update_thread.progress_updated.connect(self.on_progress_updated)
        self.update_thread.log_updated.connect(self.log_message)
        self.update_thread.finished_signal.connect(self.on_update_finished)

        # 更新UI状态
        self.set_updating_state(True)
        self.log_message(f"🚀 开始更新任务: {update_type}")

        # 启动线程
        self.update_thread.start()

    def stop_update(self):
        """停止更新"""
        if self.update_thread and self.update_thread.isRunning():
            self.update_thread.cancel()
            self.log_message("⏹️ 正在停止更新任务...")

    def on_progress_updated(self, config_name, current, total, message):
        """进度更新回调"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_bar.setValue(progress)
            self.progress_label.setText(f"正在更新 {config_name}: {current}/{total} {message}")

    def on_update_finished(self, result):
        """更新完成回调"""
        self.set_updating_state(False)

        if result.get("success", False):
            if "summary" in result:
                # 批量更新结果
                summary = result["summary"]
                self.log_message(f"✅ 批量更新完成！成功: {summary['successful_updates']}/{summary['total_configs']}")
            else:
                # 单个更新结果
                self.log_message(f"✅ 更新完成！{result.get('message', '')}")

            # 刷新配置树
            self.load_config_tree()
        else:
            error_msg = result.get("error", "未知错误")
            self.log_message(f"❌ 更新失败: {error_msg}")
            show_error_message(self, "更新失败", error_msg)

    def set_updating_state(self, is_updating):
        """设置更新状态"""
        self.update_selected_btn.setEnabled(not is_updating)
        self.update_all_btn.setEnabled(not is_updating)
        self.stop_btn.setEnabled(is_updating)
        self.progress_bar.setVisible(is_updating)

        if not is_updating:
            self.progress_bar.setValue(0)
            self.progress_label.setText("")

    def view_config_cache(self, config_name):
        """查看配置组缓存"""
        try:
            cache = update_manager.config_manager.get_config_cache(config_name)
            if not cache:
                show_info_message(self, "缓存信息", f"配置组 '{config_name}' 没有缓存信息")
                return

            # 构建缓存信息文本
            info_text = f"配置组: {config_name}\n"
            info_text += "=" * 50 + "\n"

            last_update = cache.get("last_update")
            if last_update:
                try:
                    dt = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    info_text += f"最后更新: {dt.strftime('%Y-%m-%d %H:%M:%S')}\n"
                except:
                    info_text += f"最后更新: {last_update}\n"
            else:
                info_text += "最后更新: 从未更新\n"

            info_text += f"文章总数: {cache.get('total_articles', 0)}\n"
            info_text += f"成功率: {cache.get('success_rate', 0.0):.1%}\n\n"

            last_urls = cache.get("last_urls", [])
            if last_urls:
                info_text += "最近3条URL:\n"
                info_text += "-" * 30 + "\n"
                for i, url_info in enumerate(last_urls, 1):
                    info_text += f"{i}. {url_info.get('title', '无标题')}\n"
                    info_text += f"   URL: {url_info.get('url', '')}\n"
                    timestamp = url_info.get('timestamp', '')
                    if timestamp:
                        try:
                            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                            info_text += f"   时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}\n"
                        except:
                            info_text += f"   时间: {timestamp}\n"
                    info_text += "\n"
            else:
                info_text += "暂无URL记录\n"

            # 显示信息对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("缓存信息")
            msg_box.setText(info_text)
            msg_box.setDetailedText(f"完整缓存数据:\n{cache}")
            msg_box.exec_()

        except Exception as e:
            show_error_message(self, "错误", f"查看缓存失败: {e}")

    def clear_config_cache(self, config_name):
        """清空配置组缓存"""
        reply = QMessageBox.question(
            self, "确认", f"确定要清空配置组 '{config_name}' 的缓存吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = update_manager.config_manager.clear_config_cache(config_name)
                if success:
                    show_info_message(self, "成功", f"配置组 '{config_name}' 的缓存已清空")
                    self.load_config_tree()  # 刷新树
                    self.log_message(f"🗑️ 已清空配置组 '{config_name}' 的缓存")
                else:
                    show_error_message(self, "失败", "清空缓存失败")
            except Exception as e:
                show_error_message(self, "错误", f"清空缓存失败: {e}")

    def refresh_status(self):
        """刷新状态"""
        if self.auto_refresh_check.isChecked() and not (self.update_thread and self.update_thread.isRunning()):
            # 只在没有更新任务时刷新
            self.load_config_tree()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def closeEvent(self, event):
        """关闭事件"""
        if self.update_thread and self.update_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认", "更新任务正在运行，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.update_thread.cancel()
                self.update_thread.wait(3000)  # 等待3秒
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
