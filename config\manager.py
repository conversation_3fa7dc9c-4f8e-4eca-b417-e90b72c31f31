import json
import os
from collections import OrderedDict

class ConfigManager:
    def __init__(self, config_file="configs/app/config.json", max_groups=100):
        self.config_file = config_file
        self.max_groups = max_groups
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件，如果不存在则创建默认配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f, object_pairs_hook=OrderedDict)
                    print(f"✅ 成功加载配置文件: {self.config_file}")
                    return config
            except json.JSONDecodeError as e:
                print(f"❌ JSON格式错误 {self.config_file}: {e}")
                print("🔧 使用默认配置")
                return self.create_default_config()
            except Exception as e:
                print(f"❌ 加载配置文件失败 {self.config_file}: {e}")
                print("🔧 使用默认配置")
                return self.create_default_config()
        else:
            print(f"⚠️ 配置文件不存在: {self.config_file}")
            print("🔧 创建默认配置")
            return self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置，包含所有参数字段"""
        return OrderedDict({
            "last_used": "default",
            "groups": OrderedDict({
                "default": {
                    "input_url": "",
                    "base_url": "",
                    "max_pages": "0",
                    "list_container_selector": "",
                    "article_item_selector": "",
                    "title_selectors": [],  # 使用复数形式，支持多选择器
                    "content_selectors": [],
                    "content_type": "CSS",
                    "date_selectors": [],  # 使用复数形式，支持多选择器
                    "source_selectors": [],  # 使用复数形式，支持多选择器
                    "page_suffix": "index_{n}.html",
                    "page_suffix_start": 1,
                    "url_mode": "absolute",
                    "browser": "Firefox",
                    "headless": True,
                    "window_size": "",
                    "page_load_strategy": "normal",
                    "collect_links": True,
                    "mode": "balance",
                    "filters": [],
                    "export_filename": "",
                    "classid": "",
                    "file_format": "CSV"
                }
            })
        })
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_groups(self):
        """获取所有配置组名称"""
        return list(self.config["groups"].keys())
    
    def get_group(self, group_name):
        """获取指定配置组"""
        return self.config["groups"].get(group_name)
    
    def get_current_group(self):
        """获取当前使用的配置组"""
        return self.config["last_used"]
    
    def add_group(self, group_name, config_data):
        """添加新的配置组（合并旧配置，防止字段丢失）"""
        try:
            groups = self.config["groups"]

            # 合并旧配置，防止字段丢失
            if group_name in groups:
                old_config = groups[group_name]
                merged_config = old_config.copy()
                # 嵌套字典（如module_config、pagination_config）也要合并
                for k, v in config_data.items():
                    if isinstance(v, dict) and k in merged_config and isinstance(merged_config[k], dict):
                        merged = merged_config[k].copy()
                        merged.update(v)
                        merged_config[k] = merged
                    else:
                        merged_config[k] = v
                groups[group_name] = merged_config
            else:
                # 如果达到最大组数限制，删除最旧的一个
                if len(groups) >= self.max_groups:
                    oldest = next(iter(groups))
                    del groups[oldest]
                groups[group_name] = config_data

            self.config["last_used"] = group_name
            self.save_config()
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def delete_group(self, group_name):
        """删除配置组"""
        if group_name in self.config["groups"]:
            del self.config["groups"][group_name]
            
            # 如果删除的是当前使用的配置组，重置为默认
            if self.config["last_used"] == group_name:
                self.config["last_used"] = "default" if "default" in self.config["groups"] else next(iter(self.config["groups"].keys()), None)
            
            self.save_config()
            return True
        return False
    
    def set_current_group(self, group_name):
        """设置当前使用的配置组"""
        if group_name in self.config["groups"]:
            self.config["last_used"] = group_name
            self.save_config()
            return True
        return False