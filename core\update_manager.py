#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新模块核心管理器
负责配置组的增量更新、缓存管理、URL记录等功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from config.manager import ConfigManager
from gui.config_manager import GUIConfigManager
from core.crawler import crawl_articles_async

logger = logging.getLogger(__name__)


class UpdateManager:
    """更新模块核心管理器"""
    
    def __init__(self):
        """初始化更新管理器"""
        self.config_manager = ConfigManager()
        self.gui_config_manager = GUIConfigManager()
        self.is_updating = False
        self.update_progress = {}
        
    def get_all_categories(self) -> Dict[str, Any]:
        """获取所有分类及其配置组"""
        categories = self.config_manager.get_categories()
        result = {}
        
        for category_name, category_info in categories.items():
            configs = []
            for config_name in category_info.get("configs", []):
                config = self.config_manager.get_group(config_name)
                if config:
                    cache = self.config_manager.get_config_cache(config_name)
                    configs.append({
                        "name": config_name,
                        "config": config,
                        "cache": cache or {},
                        "last_update": cache.get("last_update") if cache else None,
                        "total_articles": cache.get("total_articles", 0) if cache else 0,
                        "success_rate": cache.get("success_rate", 0.0) if cache else 0.0
                    })
            
            result[category_name] = {
                "info": category_info,
                "configs": configs
            }
        
        return result
    
    def get_config_update_info(self, config_name: str) -> Dict[str, Any]:
        """获取配置组的更新信息"""
        cache = self.config_manager.get_config_cache(config_name)
        if not cache:
            return {
                "last_update": None,
                "last_urls": [],
                "total_articles": 0,
                "success_rate": 0.0,
                "can_update": True,
                "update_reason": "首次爬取"
            }
        
        last_update = cache.get("last_update")
        last_urls = cache.get("last_urls", [])
        
        # 判断是否需要更新
        can_update = True
        update_reason = "可以更新"
        
        if last_update:
            try:
                last_update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                time_diff = datetime.now() - last_update_time
                
                if time_diff < timedelta(minutes=30):
                    can_update = False
                    update_reason = f"距离上次更新仅{int(time_diff.total_seconds()/60)}分钟，建议30分钟后再更新"
            except:
                pass
        
        return {
            "last_update": last_update,
            "last_urls": last_urls,
            "total_articles": cache.get("total_articles", 0),
            "success_rate": cache.get("success_rate", 0.0),
            "can_update": can_update,
            "update_reason": update_reason
        }
    
    async def update_single_config(
        self, 
        config_name: str, 
        progress_callback: Optional[Callable] = None,
        log_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """更新单个配置组"""
        try:
            if log_callback:
                log_callback(f"🔄 开始更新配置组: {config_name}")
            
            # 获取配置
            config = self.config_manager.get_group(config_name)
            if not config:
                error_msg = f"配置组 {config_name} 不存在"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return {"success": False, "error": error_msg}
            
            # 获取缓存信息
            cache = self.config_manager.get_config_cache(config_name)
            last_urls = cache.get("last_urls", []) if cache else []
            
            # 准备爬虫配置
            crawler_config = self.gui_config_manager.prepare_crawler_config(config)

            # 移除crawl_articles_async不支持的参数
            unsupported_params = ['pagination_config']
            for param in unsupported_params:
                crawler_config.pop(param, None)

            # 设置增量更新参数
            if last_urls:
                # 如果有缓存，只爬取最新的几页
                crawler_config["max_pages"] = min(int(crawler_config.get("max_pages", 5)), 3)
                if log_callback:
                    log_callback(f"📊 检测到缓存记录，将进行增量更新（最多{crawler_config['max_pages']}页）")
            
            # 执行爬取
            start_time = datetime.now()
            
            def update_progress(current, total, message=""):
                if progress_callback:
                    progress_callback(config_name, current, total, message)
            
            def update_log(message):
                if log_callback:
                    log_callback(f"  {message}")
            
            # 调用爬虫
            result = await crawl_articles_async(
                **crawler_config,
                progress_callback=update_progress,
                log_callback=update_log
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 更新缓存
            if result.get("success", False):
                articles = result.get("articles", [])
                new_articles = []
                
                # 过滤出新文章（不在缓存中的）
                cached_urls = {url_info["url"] for url_info in last_urls}
                for article in articles:
                    if article.get("url") not in cached_urls:
                        new_articles.append(article)
                
                # 更新缓存信息
                cache_update = {
                    "last_update": datetime.now().isoformat(),
                    "total_articles": cache.get("total_articles", 0) + len(new_articles) if cache else len(articles),
                    "success_rate": result.get("success_rate", 1.0)
                }
                
                # 添加最新的URL到缓存
                for article in articles[:3]:  # 只保存最新3条
                    self.config_manager.add_url_to_cache(
                        config_name,
                        article.get("url", ""),
                        article.get("title", ""),
                        article.get("date", datetime.now().isoformat())
                    )
                
                self.config_manager.update_config_cache(config_name, cache_update)
                
                success_msg = f"✅ 配置组 {config_name} 更新完成"
                success_msg += f" | 新增文章: {len(new_articles)} 篇"
                success_msg += f" | 用时: {duration:.1f}秒"
                
                if log_callback:
                    log_callback(success_msg)
                
                return {
                    "success": True,
                    "new_articles": len(new_articles),
                    "total_articles": len(articles),
                    "duration": duration,
                    "message": success_msg
                }
            else:
                error_msg = f"❌ 配置组 {config_name} 更新失败: {result.get('error', '未知错误')}"
                if log_callback:
                    log_callback(error_msg)
                return {"success": False, "error": error_msg}
                
        except Exception as e:
            error_msg = f"❌ 更新配置组 {config_name} 时发生异常: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            logger.exception(f"更新配置组失败: {config_name}")
            return {"success": False, "error": error_msg}
    
    async def update_multiple_configs(
        self, 
        config_names: List[str], 
        progress_callback: Optional[Callable] = None,
        log_callback: Optional[Callable] = None,
        max_concurrent: int = 3
    ) -> Dict[str, Any]:
        """批量更新多个配置组"""
        if self.is_updating:
            return {"success": False, "error": "已有更新任务在进行中"}
        
        self.is_updating = True
        self.update_progress = {}
        
        try:
            if log_callback:
                log_callback(f"🚀 开始批量更新 {len(config_names)} 个配置组")
            
            results = {}
            total_new_articles = 0
            successful_updates = 0
            
            # 创建信号量限制并发数
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def update_with_semaphore(config_name):
                async with semaphore:
                    return await self.update_single_config(
                        config_name, 
                        progress_callback, 
                        log_callback
                    )
            
            # 并发执行更新
            tasks = [update_with_semaphore(name) for name in config_names]
            update_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(update_results):
                config_name = config_names[i]
                if isinstance(result, Exception):
                    results[config_name] = {
                        "success": False, 
                        "error": str(result)
                    }
                else:
                    results[config_name] = result
                    if result.get("success", False):
                        successful_updates += 1
                        total_new_articles += result.get("new_articles", 0)
            
            summary_msg = f"🎉 批量更新完成！"
            summary_msg += f" 成功: {successful_updates}/{len(config_names)}"
            summary_msg += f" | 新增文章: {total_new_articles} 篇"
            
            if log_callback:
                log_callback(summary_msg)
            
            return {
                "success": True,
                "results": results,
                "summary": {
                    "total_configs": len(config_names),
                    "successful_updates": successful_updates,
                    "total_new_articles": total_new_articles
                },
                "message": summary_msg
            }
            
        except Exception as e:
            error_msg = f"❌ 批量更新失败: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            logger.exception("批量更新失败")
            return {"success": False, "error": error_msg}
        finally:
            self.is_updating = False
            self.update_progress = {}
    
    async def update_all_configs(
        self, 
        progress_callback: Optional[Callable] = None,
        log_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """更新所有配置组"""
        all_configs = []
        categories = self.get_all_categories()
        
        for category_name, category_data in categories.items():
            for config_info in category_data["configs"]:
                all_configs.append(config_info["name"])
        
        if not all_configs:
            return {"success": False, "error": "没有找到任何配置组"}
        
        return await self.update_multiple_configs(
            all_configs, 
            progress_callback, 
            log_callback
        )
    
    def stop_update(self):
        """停止更新（设置标志位）"""
        self.is_updating = False
        logger.info("更新任务已停止")
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        return {
            "is_updating": self.is_updating,
            "progress": self.update_progress.copy()
        }


# 全局更新管理器实例
update_manager = UpdateManager()
